import { openrouter } from "@openrouter/ai-sdk-provider";
import { generateText } from "ai";
import type { Env } from "../../types/env";

// OpenRouter model configurations
export const MODELS = {
  BASIC: "google/gemini-flash-1.5",
  ADVANCED: "openai/gpt-4-turbo",
  CONVERSATION: "google/gemini-flash-1.5"
} as const;

// Rate limiting configuration
export const RATE_LIMITS = {
  MAX_REQUESTS_PER_MINUTE: 60,
  MAX_TOKENS_PER_REQUEST: 4000,
  TIMEOUT_MS: 30000
} as const;

// Error types for better error handling
export class OpenRouterError extends Error {
  constructor(
    message: string,
    public statusCode: number = 500,
    public code: string = "OPENROUTER_ERROR"
  ) {
    super(message);
    this.name = "OpenRouterError";
  }
}

export class ValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "ValidationError";
  }
}

// Shared OpenRouter client with error handling and logging
export class OpenRouterClient {
  private apiKey: string;

  constructor(env: Env) {
    if (!env.OPENROUTER_API_KEY) {
      throw new OpenRouterError("OpenRouter API key not found in environment", 500, "MISSING_API_KEY");
    }
    this.apiKey = env.OPENROUTER_API_KEY;
    console.log("🔑 OpenRouter client initialized successfully");
  }

  async generateText(options: {
    model: string;
    messages: Array<{ role: string; content: string | Array<any> }>;
    maxTokens?: number;
    temperature?: number;
    systemPrompt?: string;
  }): Promise<{ text: string; usage?: any }> {
    const startTime = Date.now();
    
    try {
      console.log(`🤖 Calling OpenRouter API with model: ${options.model}`);
      console.log(`📝 Request details: ${options.messages.length} messages, maxTokens: ${options.maxTokens || 'default'}`);

      // Create model instance
      const model = openrouter(options.model);

      // Prepare messages with optional system prompt
      const messages = options.systemPrompt 
        ? [{ role: "system", content: options.systemPrompt }, ...options.messages]
        : options.messages;

      // Make the API call
      const result = await generateText({
        model,
        messages: messages as any,
        maxTokens: options.maxTokens || RATE_LIMITS.MAX_TOKENS_PER_REQUEST,
        temperature: options.temperature || 0.3,
      });

      const processingTime = Date.now() - startTime;
      console.log(`✅ OpenRouter API call completed in ${processingTime}ms`);
      console.log(`📊 Response length: ${result.text.length} characters`);

      return {
        text: result.text,
        usage: result.usage
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      console.error(`❌ OpenRouter API call failed after ${processingTime}ms:`, error);
      
      if (error instanceof Error) {
        // Handle specific OpenRouter errors
        if (error.message.includes("rate limit")) {
          throw new OpenRouterError("Rate limit exceeded. Please try again later.", 429, "RATE_LIMIT");
        }
        if (error.message.includes("insufficient credits")) {
          throw new OpenRouterError("Insufficient OpenRouter credits.", 402, "INSUFFICIENT_CREDITS");
        }
        if (error.message.includes("invalid model")) {
          throw new OpenRouterError("Invalid model specified.", 400, "INVALID_MODEL");
        }
        
        throw new OpenRouterError(`OpenRouter API error: ${error.message}`, 500, "API_ERROR");
      }
      
      throw new OpenRouterError("Unknown error occurred", 500, "UNKNOWN_ERROR");
    }
  }
}

// Utility functions for request validation
export function validateImageInput(image: string): void {
  if (!image) {
    throw new ValidationError("Image data is required");
  }
  
  if (typeof image !== "string") {
    throw new ValidationError("Image must be a base64 string");
  }
  
  // Check if it's a valid base64 image
  const base64Regex = /^data:image\/(jpeg|jpg|png|gif|webp);base64,/;
  if (!base64Regex.test(image)) {
    throw new ValidationError("Image must be a valid base64 encoded image (jpeg, jpg, png, gif, webp)");
  }
  
  // Check size (rough estimate: base64 is ~1.33x original size)
  const sizeInBytes = (image.length * 3) / 4;
  const maxSizeInMB = 10;
  if (sizeInBytes > maxSizeInMB * 1024 * 1024) {
    throw new ValidationError(`Image size exceeds ${maxSizeInMB}MB limit`);
  }
}

export function validateTextInput(text: string, fieldName: string = "text"): void {
  if (!text) {
    throw new ValidationError(`${fieldName} is required`);
  }
  
  if (typeof text !== "string") {
    throw new ValidationError(`${fieldName} must be a string`);
  }
  
  if (text.trim().length === 0) {
    throw new ValidationError(`${fieldName} cannot be empty`);
  }
  
  // Check length limits
  const maxLength = 10000; // 10k characters
  if (text.length > maxLength) {
    throw new ValidationError(`${fieldName} exceeds maximum length of ${maxLength} characters`);
  }
}

// Standard response helpers
export function createSuccessResponse(data: any, processingTime: number) {
  return new Response(JSON.stringify({
    success: true,
    data,
    processingTime,
    timestamp: new Date().toISOString()
  }), {
    status: 200,
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type"
    }
  });
}

export function createErrorResponse(
  message: string, 
  statusCode: number = 500, 
  processingTime: number = 0,
  code?: string
) {
  console.error(`❌ API Error (${statusCode}): ${message}`);
  
  return new Response(JSON.stringify({
    success: false,
    error: message,
    code,
    processingTime,
    timestamp: new Date().toISOString()
  }), {
    status: statusCode,
    headers: {
      "Content-Type": "application/json",
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type"
    }
  });
}

// CORS handler for preflight requests
export function handleCORS() {
  return new Response(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type"
    }
  });
}
