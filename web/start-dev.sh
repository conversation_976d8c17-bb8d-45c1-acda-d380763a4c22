#!/bin/bash

echo "🚀 Starting TinderOP Development Servers..."

# Kill any existing wrangler processes
echo "🔪 Killing existing wrangler processes..."
pkill -f "wrangler" || true

# Wait a moment
sleep 1

# Start the API server in the background
echo "🌐 Starting Cloudflare Workers API server on port 8788..."
npx wrangler pages dev dist --port 8788 --compatibility-date=2025-04-03 --compatibility-flags nodejs_compat &

# Wait for API server to start
echo "⏳ Waiting for API server to start..."
sleep 5

# Check if API server is running
if lsof -i :8788 > /dev/null; then
    echo "✅ API server is running on http://localhost:8788"
else
    echo "❌ Failed to start API server"
    exit 1
fi

# Start the Vite dev server
echo "🎨 Starting Vite development server on port 5173..."
bun run dev