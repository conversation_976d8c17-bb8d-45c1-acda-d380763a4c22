{"name": "tinderop", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "preview": "vite preview", "typecheck": "tsc --noEmit", "deploy": "bun run build && wrangler deploy", "deploy:preview": "bun run build && wrangler deploy --dry-run", "wrangler": "wrangler", "lint": "biome check src", "lint:fix": "biome check src --write", "format": "biome format src --write", "check": "biome check src --verbose", "check:fix": "biome check src --write --verbose", "dev:api": "wrangler pages dev dist --port 8788 --compatibility-date=2025-04-03 --compatibility-flags nodejs_compat", "dev:all": "concurrently \"bun run dev\" \"bun run dev:api\""}, "dependencies": {"@ai-sdk/openai": "latest", "@clerk/react-router": "^1.6.3", "@emotion/is-prop-valid": "latest", "@hookform/resolvers": "^3.9.1", "@openrouter/ai-sdk-provider": "^0.7.2", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "latest", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "latest", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@react-router/dev": "^7.0.1", "@react-router/node": "^7.0.1", "@react-router/serve": "^7.0.1", "ai": "latest", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^3.6.0", "embla-carousel-react": "8.5.1", "framer-motion": "latest", "input-otp": "1.4.1", "isbot": "^4.4.0", "lucide-react": "^0.454.0", "next-themes": "^0.4.6", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-dropzone": "latest", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "react-router": "^7.0.1", "recharts": "2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "latest"}, "devDependencies": {"@biomejs/biome": "^2.0.6", "@cloudflare/vite-plugin": "^1.7.5", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^4.3.4", "concurrently": "^9.2.0", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5", "vite": "npm:rolldown-vite@latest", "wrangler": "^4.22.0"}}