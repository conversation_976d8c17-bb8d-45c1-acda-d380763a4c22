import type { BioAnalysisProgress, BioAnalysisResult, StepR<PERSON>ult } from "@/types/analysis";
import { BIO_ANALYSIS_STEPS } from "@/types/analysis";
import { getApiUrl } from './api-config';

export interface BioAnalysisCallbacks {
  onProgress?: (progress: BioAnalysisProgress) => void;
  onStepComplete?: (stepResult: StepResult) => void;
  onComplete?: (result: BioAnalysisResult) => void;
  onError?: (error: string) => void;
}

export class BioAnalysisAgent {
  constructor() {
    console.log("🔑 BioAnalysisAgent initialized - using server-side API");
  }

  // Legacy step prompts - now handled by server-side API

  async analyzeBio(
    bio: string,
    onProgress?: (stepId: number, stepName: string, progress: number) => void
  ): Promise<StepResult[]> {
    const startTime = Date.now();

    try {
      console.log(`🔍 Starting server-side bio analysis: ${bio.length} characters`);

      // Prepare the request payload
      const requestBody = {
        bio,
        options: {
          includeRecommendations: true,
          includeImprovedBio: false, // Don't include improved bio in basic analysis
          maxTokensPerStep: 800
        }
      };

      // Simulate progress for the overall API call
      onProgress?.(1, "Initializing Analysis", 10);

      // Call the server-side API endpoint
      const response = await fetch(getApiUrl('ANALYZE_BIO'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({})) as any;
        throw new Error(errorData.error || `Server error: ${response.status}`);
      }

      const data = await response.json() as any;

      if (!data.success) {
        throw new Error(data.error || 'Bio analysis failed');
      }

      const results = data.data.steps;
      const processingTime = Date.now() - startTime;

      // Simulate progress updates for each step
      for (let i = 0; i < results.length; i++) {
        const step = results[i];
        const progress = ((i + 1) / results.length) * 100;
        onProgress?.(step.stepId, step.stepName, progress);

        console.log(`📊 Bio Analysis Step ${step.stepId} (${step.stepName}):`, {
          bio: bio.substring(0, 100) + '...',
          score: step.score,
          insights: step.insights,
          confidence: step.confidence,
          processingTime: `${step.processingTime}ms`
        });

        // Small delay to show progress
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      console.log(`📊 FINAL BIO ANALYSIS RESULTS:`, {
        overallScore: data.data.overallScore,
        stepScores: results.map((r: StepResult) => ({ step: r.stepName, score: r.score })),
        totalSteps: results.length,
        totalProcessingTime: `${processingTime}ms`
      });

      return results;

    } catch (error) {
      console.error('❌ Bio analysis failed:', error);

      // Return error results for all steps
      const errorResults: StepResult[] = BIO_ANALYSIS_STEPS.map(step => ({
        stepId: step.id,
        stepName: step.name,
        score: 0,
        insights: ["Analysis failed. Please try again."],
        confidence: 0,
        processingTime: 0,
      }));

      return errorResults;
    }
  }

  calculateOverallScore(stepResults: StepResult[]): number {
    if (stepResults.length === 0) return 0;
    
    // Weighted average - some steps matter more for overall appeal
    const weights = {
      1: 0.15, // Writing Quality - 15%
      2: 0.30, // Personality Appeal - 30% (most important)
      3: 0.20, // Interest Analysis - 20%
      4: 0.15, // Dating Intent - 15%
      5: 0.20, // Engagement Factor - 20%
    };

    let weightedSum = 0;
    let totalWeight = 0;

    stepResults.forEach(result => {
      const weight = weights[result.stepId as keyof typeof weights] || 0.2;
      weightedSum += result.score * weight;
      totalWeight += weight;
    });

    return Math.round(weightedSum / totalWeight);
  }

  generateFinalRecommendations(stepResults: StepResult[]): string[] {
    const recommendations: string[] = [];
    
    // Find the lowest scoring areas and add targeted recommendations
    const sortedByScore = [...stepResults].sort((a, b) => a.score - b.score);
    
    sortedByScore.slice(0, 3).forEach(step => {
      if (step.score < 70) {
        recommendations.push(`Improve ${step.stepName.toLowerCase()}: ${step.insights[0]}`);
      }
    });

    // Add general recommendations based on overall pattern
    const averageScore = stepResults.reduce((sum, step) => sum + step.score, 0) / stepResults.length;
    
    if (averageScore < 50) {
      recommendations.push("Consider a complete bio rewrite focusing on your best qualities");
    } else if (averageScore < 70) {
      recommendations.push("Good foundation - polish specific areas for better impact");
    }

    // Ensure we have at least 3 recommendations
    while (recommendations.length < 3 && stepResults.length > 0) {
      const randomStep = stepResults[Math.floor(Math.random() * stepResults.length)];
      const insight = randomStep.insights[Math.floor(Math.random() * randomStep.insights.length)];
      if (!recommendations.some(rec => rec.includes(insight))) {
        recommendations.push(insight);
      }
    }

    return recommendations.slice(0, 5); // Max 5 recommendations
  }

  async generateImprovedBio(
    originalBio: string,
    stepResults: StepResult[],
    tone: "witty" | "sincere" | "adventurous" = "sincere"
  ): Promise<string> {
    try {
      console.log(`🔄 Generating improved bio with ${tone} tone...`);

      // Prepare the request payload
      const requestBody = {
        bio: originalBio,
        options: {
          includeRecommendations: false,
          includeImprovedBio: true,
          tone,
          maxTokensPerStep: 800
        }
      };

      // Call the server-side API endpoint
      const response = await fetch(getApiUrl('ANALYZE_BIO'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({})) as any;
        throw new Error(errorData.error || `Server error: ${response.status}`);
      }

      const data = await response.json() as any;

      if (!data.success) {
        throw new Error(data.error || 'Bio improvement failed');
      }

      const improvedBio = data.data.improvedBio;

      if (!improvedBio) {
        throw new Error('No improved bio was generated');
      }

      console.log('✅ Improved bio generated successfully');
      return improvedBio;

    } catch (error) {
      console.error('❌ Failed to generate improved bio:', error);

      // Fallback: return a simple improvement message
      return `Here's a suggestion for improving your bio based on the analysis. Focus on addressing the areas that scored lowest: ${stepResults
        .filter(step => step.score < 60)
        .map(step => step.stepName.toLowerCase())
        .join(', ')}.`;
    }
  }
}

export const bioAnalysisAgent = new BioAnalysisAgent();