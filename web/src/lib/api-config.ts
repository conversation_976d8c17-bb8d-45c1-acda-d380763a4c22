// API Configuration for TinderOP
// Handles environment-specific API base URLs

/**
 * Get the appropriate API base URL based on the current environment
 * In development: Use Cloudflare Workers dev server (localhost:8788)
 * In production: Use relative URLs (same domain)
 */
export function getApiBaseUrl(): string {
  // Check if we're in development mode
  const isDev = (import.meta as any).env?.DEV || 
                (typeof window !== 'undefined' && window.location.hostname === 'localhost');
  
  if (isDev) {
    // In development, use Cloudflare Workers dev server
    return 'http://localhost:8788';
  }
  
  // In production, use relative URLs (same domain)
  return '';
}

/**
 * Build a complete API URL for the given endpoint
 * @param endpoint - The API endpoint path (e.g., '/api/analyze/bio')
 * @returns Complete URL for the API endpoint
 */
export function buildApiUrl(endpoint: string): string {
  const baseUrl = getApiBaseUrl();
  return `${baseUrl}${endpoint}`;
}

/**
 * API endpoints configuration
 */
export const API_ENDPOINTS = {
  // Basic analysis endpoints
  ANALYZE_IMAGE: '/api/analyze/image',
  ANALYZE_BIO: '/api/analyze/bio',
  
  // Advanced analysis endpoints
  ANALYZE_IMAGE_PRO: '/api/analyze/image-pro',
  ANALYZE_BIO_PRO: '/api/analyze/bio-pro',
  
  // Conversation analysis
  ANALYZE_CONVERSATION: '/api/tinder-helper/analyze',
  
  // Health check
  HEALTH: '/api/health'
} as const;

/**
 * Get the complete URL for a specific API endpoint
 * @param endpoint - The endpoint key from API_ENDPOINTS
 * @returns Complete URL for the endpoint
 */
export function getApiUrl(endpoint: keyof typeof API_ENDPOINTS): string {
  return buildApiUrl(API_ENDPOINTS[endpoint]);
}

/**
 * Development server configuration
 */
export const DEV_CONFIG = {
  CLOUDFLARE_WORKERS_PORT: 8788,
  VITE_DEV_PORT: 5173
} as const;

/**
 * Check if the current environment is development
 */
export function isDevelopment(): boolean {
  return (import.meta as any).env?.DEV || 
         (typeof window !== 'undefined' && window.location.hostname === 'localhost');
}

/**
 * Check if we're running in the Vite dev server
 */
export function isViteDevServer(): boolean {
  return typeof window !== 'undefined' && 
         window.location.port === DEV_CONFIG.VITE_DEV_PORT.toString();
}

/**
 * Check if we're running in the Cloudflare Workers dev server
 */
export function isCloudflareDevServer(): boolean {
  return typeof window !== 'undefined' && 
         window.location.port === DEV_CONFIG.CLOUDFLARE_WORKERS_PORT.toString();
}
