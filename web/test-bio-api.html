<!DOCTYPE html>
<html>
<head>
    <title>Bio API Test</title>
</head>
<body>
    <h1>Bio Analysis API Test</h1>
    <button onclick="testBioAnalysis()">Test Bio Analysis</button>
    <div id="result"></div>

    <script>
        async function testBioAnalysis() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('http://localhost:8788/api/analyze/bio', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        bio: "Adventure enthusiast who loves hiking, cooking, and exploring new places. Looking for someone to share life moments with."
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    resultDiv.innerHTML = `
                        <h2>Success!</h2>
                        <p>Overall Score: ${data.data.overallScore}/100</p>
                        <p>Processing Time: ${data.data.processingTime}ms</p>
                        <h3>Step Results:</h3>
                        <ul>
                        ${data.data.steps.map(step => 
                            `<li>${step.stepName}: ${step.score}/100</li>`
                        ).join('')}
                        </ul>
                    `;
                } else {
                    resultDiv.innerHTML = `<p style="color: red;">Error: ${data.error}</p>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<p style="color: red;">Network Error: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>