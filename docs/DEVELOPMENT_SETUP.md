# Development Setup Guide

## Overview

TinderOP now uses a server-side API architecture with Cloudflare Workers for enhanced security. This guide explains how to set up and run the development environment correctly.

## Architecture

- **Frontend**: Vite development server (localhost:5173)
- **Backend**: Cloudflare Workers development server (localhost:8788)
- **API Endpoints**: All analysis functions run on the Cloudflare Workers server

## Prerequisites

1. **Node.js** (v18 or higher)
2. **Bun** (recommended) or npm
3. **OpenRouter API Key** - Get one from [OpenRouter](https://openrouter.ai/)

## Environment Setup

### 1. Environment Variables

Create a `.env` file in the `web` directory:

```bash
# web/.env
OPENROUTER_API_KEY=sk-or-v1-your-api-key-here
```

**Important**: The API key should be `OPENROUTER_API_KEY` (not `VITE_OPENROUTER_API_KEY`) since it's now server-side only.

### 2. Install Dependencies

```bash
cd web
bun install
```

## Running the Development Environment

### Option 1: Cloudflare Workers Development Server (Recommended)

This runs both the frontend and backend together:

```bash
cd web
bun run dev
```

This will start:
- Cloudflare Workers dev server on `localhost:8788` (with API endpoints)
- Vite frontend will be proxied through the Workers server

**Access the app at**: `http://localhost:8788`

### Option 2: Separate Servers (Advanced)

If you need to run servers separately for debugging:

**Terminal 1 - Start Cloudflare Workers server:**
```bash
cd web
bun run wrangler pages dev dist --compatibility-date=2024-01-01 --port 8788
```

**Terminal 2 - Start Vite development server:**
```bash
cd web
bun run vite
```

**Access the app at**: `http://localhost:5173` (frontend will call APIs on localhost:8788)

## API Endpoints

The following endpoints are available on the Cloudflare Workers server:

- `POST /api/analyze/image` - Basic image analysis
- `POST /api/analyze/bio` - Basic bio analysis  
- `POST /api/analyze/image-pro` - Advanced image analysis
- `POST /api/analyze/bio-pro` - Advanced bio analysis
- `POST /api/tinder-helper/analyze` - Conversation analysis
- `GET /api/health` - Health check

## Troubleshooting

### 405 Method Not Allowed Errors

If you see errors like:
```
❌ Bio analysis failed: Error: Server error: 405
```

**Cause**: The frontend is trying to call API endpoints on the Vite server (localhost:5173) instead of the Cloudflare Workers server (localhost:8788).

**Solution**: Make sure you're accessing the app through `http://localhost:8788` (not localhost:5173).

### API Key Issues

If you see errors about missing API keys:
```
❌ OpenRouter API key not found in environment
```

**Solution**: 
1. Make sure your `.env` file is in the `web` directory
2. Use `OPENROUTER_API_KEY` (not `VITE_OPENROUTER_API_KEY`)
3. Restart the development server after adding the API key

### Port Conflicts

If port 8788 is already in use:
```bash
# Kill any process using port 8788
lsof -ti:8788 | xargs kill -9

# Or use a different port
bun run wrangler pages dev dist --port 8789
```

### Build Issues

If you encounter build issues:
```bash
# Clean and reinstall dependencies
rm -rf node_modules
rm bun.lockb
bun install

# Clear Vite cache
rm -rf .vite
```

## Testing the Setup

### 1. Health Check

Visit `http://localhost:8788/api/health` - you should see a JSON response.

### 2. Run Test Suite

```bash
# Make test script executable
chmod +x test/run-tests.sh

# Run comprehensive tests
./test/run-tests.sh
```

### 3. Manual Testing

1. Go to `http://localhost:8788`
2. Try the Bio Analyzer with a sample bio
3. Try the Image Analyzer with a sample image
4. Check browser console for any errors

## Production Deployment

For production deployment to Cloudflare Pages:

1. Set the `OPENROUTER_API_KEY` environment variable in your Cloudflare Pages dashboard
2. Deploy using the standard Cloudflare Pages workflow
3. The API endpoints will be available at your domain's `/api/*` paths

## Development Tips

1. **Use the correct URL**: Always access the app via `localhost:8788` in development
2. **Check console logs**: Both browser and server console logs are helpful for debugging
3. **API key security**: The API key is never exposed to the client - it's server-side only
4. **Hot reloading**: Changes to frontend code will hot-reload, but server changes require restart

## Common Commands

```bash
# Start development server
bun run dev

# Build for production
bun run build

# Preview production build
bun run preview

# Run tests
./test/run-tests.sh

# Check TypeScript
bun run type-check
```

## Support

If you encounter issues:

1. Check this guide first
2. Look at the browser console for errors
3. Check the server logs in your terminal
4. Verify your API key is set correctly
5. Make sure you're using the correct URL (localhost:8788)
